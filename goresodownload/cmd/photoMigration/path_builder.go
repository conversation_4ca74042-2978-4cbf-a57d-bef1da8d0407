package main

import (
	"fmt"
	"strconv"
	"strings"

	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
)

var (
	storeTRB *levelStore.DirKeyStore
	storeDDF *levelStore.DirKeyStore
	storeOTW *levelStore.DirKeyStore
	storeCLG *levelStore.DirKeyStore
)

// initDirKeyStore initializes directory key stores for all supported source types
func initDirKeyStore() error {
	collection := gomongo.Coll("rni", "dir_stats")
	if collection == nil {
		return fmt.Errorf("failed to get dir_stats collection")
	}

	var err error
	storeTRB, err = levelStore.NewDirKeyStore("TRB", collection, "")
	if err != nil {
		return fmt.Errorf("failed to create TRB store: %w", err)
	}

	storeDDF, err = levelStore.NewDirKeyStore("DDF", collection, "")
	if err != nil {
		return fmt.Errorf("failed to create DDF store: %w", err)
	}

	storeOTW, err = levelStore.NewDirKeyStore("OTW", collection, "")
	if err != nil {
		return fmt.Errorf("failed to create OTW store: %w", err)
	}

	storeCLG, err = levelStore.NewDirKeyStore("CLG", collection, "")
	if err != nil {
		return fmt.Errorf("failed to create CLG store: %w", err)
	}

	golog.Info("DirKeyStore initialized successfully")
	return nil
}

// buildOriginalPath constructs the original relative path based on property content
func buildOriginalPath(params PathBuildParams) (string, error) {
	src := params.Src

	switch src {
	case SRC_DDF:
		return buildDDFPath(params)
	case SRC_OTW, SRC_CLG:
		return buildOTWCLGPath(params)
	case SRC_TRB:
		return buildTRBPath(params)
	default:
		return "", fmt.Errorf("unsupported src type: %s", src)
	}
}

// buildDDFPath constructs path for DDF source type
// Pattern: crea/ddf/img/{last3}/{ddfID}_{imageNum}.jpg
func buildDDFPath(params PathBuildParams) (string, error) {
	prop := params.Prop
	imageNum := params.ImageNum

	propID, ok := prop["_id"].(string)
	if !ok {
		return "", fmt.Errorf("invalid _id for DDF")
	}

	if len(propID) < 6 || !strings.HasPrefix(propID, "DDF") {
		return "", fmt.Errorf("invalid DDF ID format: %s", propID)
	}

	// Extract DDF ID: DDF23214174 -> 174, 23214174
	ddfID := propID[3:] // Remove DDF prefix
	if len(ddfID) < 3 {
		return "", fmt.Errorf("DDF ID too short: %s", ddfID)
	}

	last3 := ddfID[len(ddfID)-3:]
	fileName := fmt.Sprintf("%s_%d.jpg", ddfID, imageNum)

	return fmt.Sprintf("crea/ddf/img/%s/%s", last3, fileName), nil
}

// buildOTWCLGPath constructs path for OTW/CLG source types
// Pattern: oreb/mls/{mui_last34}/{mui_last12}/{propImgId}_{imageID}.jpg
func buildOTWCLGPath(params PathBuildParams) (string, error) {
	prop := params.Prop
	imageID := params.ImageID

	orgID, ok := prop["orgId"]
	if !ok {
		return "", fmt.Errorf("orgId not found for %s", params.Src)
	}

	var orgIDStr string
	switch v := orgID.(type) {
	case int:
		orgIDStr = strconv.Itoa(v)
	case int32:
		orgIDStr = strconv.Itoa(int(v))
	case int64:
		orgIDStr = strconv.Itoa(int(v))
	case string:
		orgIDStr = v
	default:
		return "", fmt.Errorf("invalid orgId type for %s", params.Src)
	}

	if len(orgIDStr) < 4 {
		return "", fmt.Errorf("orgId too short: %s", orgIDStr)
	}

	// Extract last 4 digits and split into 2-digit pairs
	last4 := orgIDStr[len(orgIDStr)-4:]
	mui_last34 := last4[:2]
	mui_last12 := last4[2:]

	propID, ok := prop["_id"].(string)
	if !ok {
		return "", fmt.Errorf("invalid _id for %s", params.Src)
	}

	if len(propID) < 3 {
		return "", fmt.Errorf("prop _id too short: %s", propID)
	}

	propImgID := propID[3:] // Remove prefix (OTW/CLG)
	fileName := fmt.Sprintf("%s_%s.jpg", propImgID, imageID)

	// Use different path prefixes for OTW and CLG
	var pathPrefix string
	if params.Src == SRC_OTW {
		pathPrefix = "oreb/mls"
	} else if params.Src == SRC_CLG {
		pathPrefix = "creb/mls"
	} else {
		return "", fmt.Errorf("unsupported src type in buildOTWCLGPath: %s", params.Src)
	}

	return fmt.Sprintf("%s/%s/%s/%s", pathPrefix, mui_last34, mui_last12, fileName), nil
}

// buildTRBPath constructs path for TRB source type
// Pattern: treb/mls/{imageNum}/{last3}/{sid}[_{imageNum}].jpg
func buildTRBPath(params PathBuildParams) (string, error) {
	prop := params.Prop
	imageNum := params.ImageNum

	sid, ok := prop["sid"].(string)
	if !ok {
		return "", fmt.Errorf("invalid sid for TRB")
	}

	if len(sid) < 3 {
		return "", fmt.Errorf("sid too short: %s", sid)
	}

	last3 := sid[len(sid)-3:]

	var fileName string
	if imageNum == 1 {
		fileName = fmt.Sprintf("%s.jpg", sid)
	} else {
		fileName = fmt.Sprintf("%s_%d.jpg", sid, imageNum)
	}

	return fmt.Sprintf("treb/mls/%d/%s/%s", imageNum, last3, fileName), nil
}

// buildNewPathWithHash generates new path using pre-calculated base path and custom hash
func buildNewPathWithHash(basePath string, sid string, customHash int32) (string, error) {
	var hash int32
	if customHash != 0 {
		hash = customHash
	} else {
		return "", fmt.Errorf("customHash is required")
	}

	base62, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return "", fmt.Errorf("failed to generate base62: %w", err)
	}

	// Construct final path: basePath/sid_base62.jpg
	fileName := fmt.Sprintf("%s_%s.jpg", sid, base62)
	return fmt.Sprintf("%s/%s", basePath, fileName), nil
}
