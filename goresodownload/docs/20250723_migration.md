📘 数据迁移任务技术方案（ca6 & ca7 文件系统）
📌 背景
我们现有两个硬盘挂载点：ca6 与 ca7，存储结构中的照片需要迁移至新目录结构。
原始数据存储信息需要根据 MongoDB 的 prop 表中按照不同board的不同规则生成。
目标存储路径需要使用golevelstore 生成l1/l2, 使用原始数据路径作为key，使用 hash := levelStore.MurmurToInt32(keyStr)计算int32 hash，并获取对应base62编码，拼接出新的文件名。
本地pic dir: L1/L2/sid_base62.jpg
迁移过程中需要记录操作状态、更新数据库字段、并根据文件存在性做不同处理策略。
支持输入参数：
  -mt "2025-07-23T15:30:00Z"
  -disk "ca6"
  -dryrun
不需要支持断点续传，在log中写入prop_id, src, ts，dst path，old path,是否成功等信息，所有信息都写log，err同时还需要报错mongo，这样中断后，可以使用ts query旧数据处理。失败的记录，不需要重试机制。
参考本package中代码，利用golevelstore，gomongo，golog，gospeedmeter等package，使用方式参考本package的代码

🎯 目标
遍历 prop 表[参考test/e2e/fixtures/listing/properties.json]中【支持使用mt过滤】记录，按 stream 倒序处理；

添加输入参数运行ca6 或 ca7；生产环境会先在ca6上运行，再在ca7上运行；

如果图片在本机执行文件移动（rename）；
如果图片不在本机，从另一个硬盘副本复制（copy）；[只适用在ca6上运行时]

单条记录处理流程：
1）function判断是否是reso
2）function获取 原始路径，目标路径
3）rename
  a. 成功
  b. 失败
    a. ca6运行时候（仅限 ca6）, 从ca7 copy fallback 处理
    b. ca7运行时候, 直接记录失败
  不是a成功的操作结果记录至 photo_immigration_ca6 与 photo_immigration_ca7 表；
  {
    "prop_id": "TRB12345678",
    "mt": "2025-07-23T15:30:00Z", #原始prop中mt
    "src": "/mnt/ca6/oldpath.jpg",
    "dst": "/mnt/new_layout/...",
    "status": "rename_failed / not_found", # renamed / copied 不需要记录
    "err_msg": "error message if any", # 这个需要吗？会有rename失败吗？如果没有，就不需要记录
    "ts": "2025-07-23T15:30:00Z" #原始prop中ts
  }
4）同步更新 prop 与 rni 表中目标路径。[
  在ca6运行完成后就更新prop，rni表，添加phoLH，tnLH，phoP
  ca7运行时直接从prop中获取到目标的phoP，然后处理，ca7运行完成后，把之前的字段"pho" "phoIDs"（根据不同board），改成对应的名称"pho_old", "phoIDs_old"]   一周后 没有问题，删除
  DB添加:
    phoHL:[int32,int32]
    tnHL: int32
    phoP: "/L1/L2"

⚙️ 核心流程概览

          +---------------------+
          |    MongoDB: prop    |
          +---------------------+
                    |
                    | stream desc + mt filter
                    v
       +------------------------------+
       | For disk in [ca6, ca7]       |
       |   For each filtered record:  |
       |     1. Skip if src includes reso
       |     2. Build original & dest path
       |     3. Try rename             |
       |        - success: log + DB    |
       |        - fail:                |
       |           • ca6 → try copy from ca7
       |           • ca7 → mark failed
       |     4. Insert log table       |
       |     5. Update prop + rni      |
       +------------------------------+
🔍 数据结构与字段说明
prop 表示例：参考test/e2e/fixtures/listing/properties.json
使用  gomongo.Coll("listing", "properties")


文件操作失败应详细记录原因；
Mongo日志记录失败情况；

🧪 测试建议
使用 3~5 条包含不同状态的记录进行 dry-run 测试；

模拟 rename 成功、失败、ca6 不存在、ca7 有备份等情况；

手动验证文件是否移动到新路径；

检查 photo_immigration_ca6/ca7 表是否准确反映操作结果；

确保 prop 和 rni 更新无误；



const OLD_CA6_PATH = "/mnt/ca6m0/mlsimgs"
const OLD_CA7_PATH = "/mnt/ca7m0/mlsimgs"
const NEW_CA6_PATH = "/mnt/ca6m0/imgs/MLS"
const NEW_CA7_PATH = "/mnt/ca7m0/imgs/MLS"
const (
	THUMBNAIL_WIDTH  = 240
	THUMBNAIL_HEIGHT = 160
)

# prop 表查询
filter := bson.M{"mt": bson.M{"$gte": mt}}   #if input mt, or filter should be empty
projection := {"mt": 1, "sid": 1, "src": 1, "ts": 1, "board": 1, phoDl:1,  "onD": 1}

# 确认是否需要做后续处理
func needToProcess(prop bson.M) bool {     # 可以修改func name，如果返回false，就不用做后续处理
  if prop["phoLH"] != nil {       # prop.pho，只ca6，因为ca6处理完成后，就都有PhoLH了
		return false
	}
  if prop["pho"] != nil && prop["pho"].(int) > 0 && (prop["src"] == "TRB"|| prop["src"] == "DDF") {
    return true
	}
  if (prop["src"] == "OTW" || prop["src"] == "CLG") && prop["phoIDs"] != nil {
    return true
	}
  return false
}

func buildOriginalPath(prop bson.M) string {
  //返回相对路径，做后rename 在加上OLD_CA6_PATH或者OLD_CA7_PATH
  // 根据 prop 内容构建原始路径
  # src: DDF26226669
    ddfID: DDF23214174   #prop._id
  /#{ddfID.substr(-3)}/#{ddfID.substr(3)}_#{num}.jpg"
  本地路径：/mnt/ca6m0/mlsimgs/crea/ddf/img/174/23214174_1.jpg,  num: [1..prop.pho]
  https://img.realmaster.com/cda/828.1698685677/669/26226669_1.jpg

  # src: OTW/CLG
  oreb/creb：OTW731271  有phoIDs
  本地路径：/mnt/ca6m0/mlsimgs/oreb/mls/80/61/731271_0.jpg
  orgId：prop.orgId，对应rni的表db.mls_oreb_master_records 中的 _id: '8098061'
  sid: '731271',
  phoIDs: [ '0', '1' ]
    mui_last34 = prop.orgId?.toString().slice(-4, -2) 80
    mui_last12 = prop.orgId?.toString().slice(-2) 61
    propImgId = prop._id.slice(3) 731271
    for imgId in prop.phoIDs
      imgUrl = "/#{mui_last34}/#{mui_last12}/#{propImgId}_#{imgId}.jpg"

  creb:  CLGC4148952 有phoIDs   旧字段最后要删除（rni）
  sid: 'C4148952'，_id: '1382288'，phoIDs: [ '0' ]
  creb  /mnt/ca6m0/mlsimgs/creb/mls/22/88/C4148952_0.jpg
  oreb  /mnt/ca6m0/mlsimgs/oreb/mls/22/88/C4148952_0.jpg

  # treb: TRBN4633567
  ml_num==prop.sid
  for num in [1..prop.pho]
    if num is 1
      /#{num}/#{ml_num.slice(-3)}/#{ml_num}.jpg"
    else
      /#{num}/#{ml_num.slice(-3)}/#{ml_num}_#{num}.jpg"
  }
  本地路径：
  /mnt/ca6m0/mlsimgs/treb/mls/1/567/1234567.jpg
  /mnt/ca6m0/mlsimgs/treb/mls/2/567/1234567_2.jpg
}

func initDirKeyStore() {
  collection := gomongo.Coll("rni", "dir_stats")
  storeTRB, err := levelStore.NewDirKeyStore(TRB, collection, 5*time.Minute)
  storeDDF, err := levelStore.NewDirKeyStore(DDF, collection, 5*time.Minute)
  storeOTW, err := levelStore.NewDirKeyStore(OTW, collection, 5*time.Minute)
  storeCLG, err := levelStore.NewDirKeyStore(CLG, collection, 5*time.Minute)


func buildNewPath(prop bson.M) string {
	// 使用 golevelstore 生成 L1/L2 路径
  // step1: 初始化：
  initDirKeyStore()
  key = buildOriginalPath(prop)  
  hash := levelStore.MurmurToInt32(key) 【这里需要保证同一个prop的所有图的hash不一致，如果一致，就在key后面添加累加数字】
  base62, err := levelStore.Int32ToBase62(hash)

  // Get file path for a property
  path, err := levelStore.GetFullFilePathForProp(prop["ts"].(time.Time), prop["src"], prop["sid"])
  path: /1225/abc12   // /l1/l2

  return fmt.Sprintf("%s/%s_%s.jpg", path,prop["sid"], base62)
}

func renameOrCopy(src, dst string) error {
	// 尝试 rename，失败则 copy
}

func updateDB(prop bson.M, newPath string) error {
	// 更新 prop 和 rni 表
  每一个prop有很多图，每一个图有一个对应的int32 hash，按照顺序组成一个list，每一个prop需要更新一个包含所有图的prop.phoHL:[int32,int32]，
  rni.tnHL: int32， 小图用	newImg, err := gofile.DownloadAndResizeImage(task.URL, THUMBNAIL_WIDTH, THUMBNAIL_HEIGHT) 生成，这里可能需要修改下gofile package中DownloadAndResizeImage func. thumbKey := 第一张图key + "-t" 然后计算tnHL，实际生成小图
  phoP: "/L1/L2"

  // 根据prop 查找到对应的rni记录
  根据prop.src， 获取rni对应的表
  TrebRecords =       COLLECTION('rni','mls_treb_master_records')
  query:{_id:prop._id}
  CrebRecords = COLLECTION('rni','mls_creb_master_records')
  query:{_id:prop.orgId}
  OrebRecords = COLLECTION('rni','mls_oreb_master_records')
  query:{_id:prop.orgId}
  DDFRecords =  COLLECTION('rni','mls_crea_ddf_records')
  query:{_id:prop._id}
}

func updateDirStats(prop bson.M, newPath string) error {
  store.AddDirStats("1225", "abc12", entityCount, fileCount)
}

func init(){
  	// Initialize base[config, logging]
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}
  	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
}

func main() {
	// 处理 prop 表中的记录
}



